#!/usr/bin/env python
"""
Test script to directly test the adjust_workers functionality
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from locations.models import Location
from queue_system.models import LocationQueueConfig
from queue_system.views import get_location_workers, stop_location_worker
import subprocess
import time

def test_worker_management():
    """Test worker start/stop functionality"""
    print("🔧 Testing Worker Management")
    print("=" * 50)
    
    # Get the location
    location = Location.objects.first()
    if not location:
        print("❌ No locations found")
        return
    
    print(f"📍 Testing with location: {location.location_name} (ID: {location.id})")
    
    # Get or create config
    config, created = LocationQueueConfig.objects.get_or_create(
        location=location,
        defaults={'max_workers': 2, 'active_workers': 0}
    )
    
    print(f"📊 Current config: {config.active_workers}/{config.max_workers} workers")
    
    # Test worker detection
    current_workers = get_location_workers(location)
    print(f"🔍 Currently detected workers: {len(current_workers)}")
    
    # Test starting a worker
    print("\n🚀 Testing worker start...")
    queue_name = f'location.{location.id}'
    worker_name = f'test_admin_worker_{location.location_name}_1'
    
    cmd = [
        'celery', '-A', 'config', 'worker',
        '--loglevel=info',
        '--concurrency=1',
        f'--queues={queue_name}',
        f'--hostname={worker_name}@%h',
        '--without-gossip',
        '--without-mingle',
        '--without-heartbeat'
    ]
    
    print(f"Command: {' '.join(cmd)}")
    
    try:
        # Start worker process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
        )
        
        print(f"Started process with PID: {process.pid}")
        
        # Wait a moment for worker to start
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ Worker process is running")
            
            # Update config
            config.active_workers += 1
            config.save()
            print(f"📊 Updated config: {config.active_workers}/{config.max_workers} workers")
            
            # Test worker detection again
            time.sleep(2)
            workers_after_start = get_location_workers(location)
            print(f"🔍 Workers detected after start: {len(workers_after_start)}")
            
            # Test stopping the worker
            print("\n🛑 Testing worker stop...")
            stopped_worker = stop_location_worker(location)
            
            if stopped_worker:
                print(f"✅ Stopped worker: {stopped_worker}")
                config.active_workers -= 1
                config.save()
                print(f"📊 Updated config: {config.active_workers}/{config.max_workers} workers")
            else:
                print("❌ Failed to stop worker")
                
        else:
            # Process failed to start
            stdout, stderr = process.communicate()
            error_msg = stderr.decode().strip() if stderr else "Process exited immediately"
            print(f"❌ Worker failed to start: {error_msg}")
            
    except Exception as e:
        print(f"❌ Error testing worker management: {e}")

if __name__ == "__main__":
    test_worker_management()
