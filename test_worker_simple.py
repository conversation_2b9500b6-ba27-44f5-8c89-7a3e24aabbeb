#!/usr/bin/env python
"""
Simple test script for worker management functionality
"""

import os
import sys
import django
import subprocess

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from locations.models import Location

def test_worker_start():
    """Test starting a worker manually"""
    print("🔍 Testing Worker Start")
    print("=" * 50)
    
    # Get first location
    location = Location.objects.first()
    if not location:
        print("❌ No locations found")
        return
    
    print(f"📍 Testing with location: {location.location_name} (ID: {location.id})")
    
    # Build worker command
    queue_name = f'location.{location.id}'
    worker_name = f'test_worker_{location.location_name}_1'
    
    cmd = [
        'celery', '-A', 'config', 'worker',
        '--loglevel=info',
        '--concurrency=1',
        f'--queues={queue_name}',
        f'--hostname={worker_name}@%h',
        '--without-gossip',
        '--without-mingle',
        '--without-heartbeat'
    ]
    
    print(f"🚀 Starting worker with command:")
    print(f"   {' '.join(cmd)}")
    
    try:
        # Start worker process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        print(f"✅ Worker started with PID: {process.pid}")
        print(f"   Queue: {queue_name}")
        print(f"   Hostname: {worker_name}")
        
        # Wait a moment
        import time
        time.sleep(3)
        
        # Check if still running
        if process.poll() is None:
            print(f"✅ Worker is still running")
            
            # Stop the worker
            print(f"🛑 Stopping worker...")
            process.terminate()
            try:
                process.wait(timeout=5)
                print(f"✅ Worker stopped gracefully")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"⚠️ Worker force killed")
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Worker exited immediately")
            if stderr:
                print(f"   Error: {stderr.decode()}")
                
    except Exception as e:
        print(f"❌ Error starting worker: {e}")

def test_celery_processes():
    """Test listing Celery processes using tasklist"""
    print("\n🔍 Testing Celery Process Detection")
    print("=" * 50)
    
    try:
        # Use tasklist to find celery processes
        result = subprocess.run(
            ['tasklist', '/FI', 'IMAGENAME eq celery.exe'],
            capture_output=True, text=True, timeout=10
        )
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            celery_lines = [line for line in lines if 'celery.exe' in line and line.strip()]
            
            print(f"Found {len(celery_lines)} Celery processes:")
            for line in celery_lines:
                parts = line.split()
                if len(parts) >= 2:
                    print(f"  - PID {parts[1]}: {parts[0]}")
        else:
            print("❌ Failed to run tasklist")
            
    except Exception as e:
        print(f"❌ Error checking processes: {e}")

if __name__ == "__main__":
    print("🚀 Simple Worker Management Test")
    print("=" * 50)
    
    test_celery_processes()
    test_worker_start()
    
    print("\n✅ Test completed")
