#!/usr/bin/env python
"""
Debug script to find running Celery workers
"""

import os
import sys
import django
import psutil

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from locations.models import Location
from queue_system.views import get_location_workers

def debug_worker_detection():
    """Debug worker detection"""
    print("🔍 Debugging Worker Detection")
    print("=" * 50)
    
    # Find all python processes that might be celery workers
    python_processes = []
    for proc in psutil.process_iter():
        try:
            proc_name = proc.name()
            if 'python' in proc_name.lower():
                try:
                    cmdline = proc.cmdline()
                    if cmdline and any('celery' in str(cmd).lower() for cmd in cmdline):
                        python_processes.append({
                            'pid': proc.pid,
                            'name': proc_name,
                            'cmdline': cmdline
                        })
                except:
                    pass
        except:
            pass

    print(f"Found {len(python_processes)} Python processes with 'celery' in command line:")
    for proc in python_processes:
        print(f"  PID {proc['pid']}: {proc['name']}")
        print(f"    Command: {' '.join(proc['cmdline'])}")
        print()
    
    # Test location worker detection
    location = Location.objects.first()
    if location:
        print(f"Testing location: {location.location_name} (ID: {location.id})")
        queue_name = f'location.{location.id}'
        print(f"Expected queue name: {queue_name}")
        
        workers = get_location_workers(location)
        print(f"get_location_workers returned: {len(workers)} workers")
        for worker in workers:
            print(f"  Worker: {worker}")
    else:
        print("No locations found")

if __name__ == "__main__":
    debug_worker_detection()
