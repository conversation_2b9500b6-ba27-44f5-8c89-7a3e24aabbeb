#!/usr/bin/env python3
"""
Test script to verify the new bot error tracking functionality.
This script tests the updated run_barbados_bot function to ensure it returns
detailed error information instead of just hardcoded messages.
"""

import os
import sys
import django
import traceback

# Add the project to the system path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django Environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from orders.models import BarbadosForm, order
from external.bots.Barbados_form.Barbados_form_1 import run_barbados_bot, BarbadosFormBot

def test_bot_error_tracking():
    """Test the new error tracking functionality"""
    print("🧪 Testing Bot Error Tracking Functionality")
    print("=" * 60)
    
    try:
        # Find a test order with BarbadosForm
        barbados_forms = BarbadosForm.objects.all()[:3]

        if not barbados_forms.exists():
            print("❌ No BarbadosForm records found for testing")
            return False

        for barbados_form in barbados_forms:
            try:
                test_order = barbados_form.order
                print(f"\n📋 Testing with Order: {test_order.id} - {test_order.first_name} {test_order.surname}")
                print(f"✅ Found BarbadosForm for order {test_order.id}")

                # Test the new bot functionality
                print(f"\n🤖 Running bot with new error tracking...")

                # Call the updated run_barbados_bot function
                result = run_barbados_bot(barbados_form)

                print(f"\n📊 Bot Result Analysis:")
                print(f"Result type: {type(result)}")

                if isinstance(result, dict):
                    print("✅ Bot returned detailed result dictionary")
                    print(f"Success: {result.get('success', 'N/A')}")
                    print(f"Last Error: {result.get('last_error', 'None')}")
                    print(f"Failed Step: {result.get('failed_step', 'None')}")

                    if result.get('error_summary'):
                        print(f"\n📝 Error Summary:")
                        print(result['error_summary'])

                    if result.get('error_details'):
                        print(f"\n🔍 Error Details:")
                        error_details = result['error_details']
                        print(f"  Error Type: {error_details.get('error_type', 'N/A')}")
                        print(f"  Failed Step: {error_details.get('failed_step', 'N/A')}")
                        print(f"  Successful Steps: {len(error_details.get('successful_steps', []))}")

                        if error_details.get('successful_steps'):
                            print(f"  Last 3 Successful Steps:")
                            for step in error_details['successful_steps'][-3:]:
                                print(f"    - {step}")

                    # Test if we can extract meaningful error information
                    if not result.get('success'):
                        print(f"\n✅ Bot failed with detailed error information (as expected)")
                        print(f"This replaces the hardcoded error message!")
                    else:
                        print(f"\n✅ Bot succeeded!")

                else:
                    print("⚠️  Bot returned old boolean format")
                    print(f"Success: {result}")

                # Test direct bot instantiation
                print(f"\n🔧 Testing direct bot instantiation...")
                bot = BarbadosFormBot(barbados_form)

                # Check if error tracking attributes exist
                if hasattr(bot, 'last_error') and hasattr(bot, 'error_details'):
                    print("✅ Bot has error tracking attributes")
                    print(f"Initial error state: {bot.last_error}")
                    print(f"Initial successful steps: {len(bot.successful_steps)}")
                else:
                    print("❌ Bot missing error tracking attributes")

                # Only test one order to avoid running the full bot
                break

            except Exception as e:
                print(f"❌ Error testing order {test_order.id}: {e}")
                print(traceback.format_exc())
                continue
        
        print(f"\n🎉 Error tracking test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print(traceback.format_exc())
        return False

def test_error_summary_generation():
    """Test the error summary generation without running the full bot"""
    print(f"\n🧪 Testing Error Summary Generation")
    print("=" * 40)
    
    try:
        # Find a test BarbadosForm
        barbados_form = BarbadosForm.objects.first()
        if not barbados_form:
            print("❌ No BarbadosForm found")
            return False
        
        # Create bot instance
        bot = BarbadosFormBot(barbados_form)
        
        # Simulate some successful steps and an error
        bot._record_success("Bot successfully initialized and navigated to form")
        bot._record_success("Selected residential status: Non-Resident/Visitor")
        bot._record_success("Selected entry method: Air")
        bot._record_success("Selected airline: American Airlines")
        bot._record_error("Gender dropdown selection", "Unable to select gender 'Male' from dropdown", "gender_dropdown_error")
        
        # Test error summary generation
        error_summary = bot.get_error_summary()
        
        print(f"✅ Generated Error Summary:")
        print(error_summary)
        
        print(f"\n✅ Error Details:")
        print(f"Last Error: {bot.last_error}")
        print(f"Failed Step: {bot.failed_step}")
        print(f"Successful Steps: {bot.successful_steps}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error summary test failed: {e}")
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    print("🚀 Starting Bot Error Tracking Tests")
    print("=" * 60)
    
    # Test 1: Error tracking functionality
    test1_result = test_bot_error_tracking()
    
    # Test 2: Error summary generation
    test2_result = test_error_summary_generation()
    
    print(f"\n📊 Test Results Summary:")
    print(f"Error Tracking Test: {'✅ PASSED' if test1_result else '❌ FAILED'}")
    print(f"Error Summary Test: {'✅ PASSED' if test2_result else '❌ FAILED'}")
    
    if test1_result and test2_result:
        print(f"\n🎉 All tests passed! The bot now provides detailed error information.")
        print(f"The hardcoded error messages have been replaced with dynamic error tracking.")
    else:
        print(f"\n❌ Some tests failed. Please check the implementation.")
