#!/usr/bin/env python
"""
Test script to test the AJAX endpoint directly
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.auth.models import User
from locations.models import Location
from queue_system.views import adjust_workers
import json

def test_ajax_endpoint():
    """Test the adjust_workers AJAX endpoint"""
    print("🌐 Testing AJAX Endpoint")
    print("=" * 50)
    
    # Create request factory
    factory = RequestFactory()
    
    # Get or create a user
    user, created = User.objects.get_or_create(
        username='testadmin',
        defaults={'is_staff': True, 'is_superuser': True}
    )
    
    # Get location
    location = Location.objects.first()
    if not location:
        print("❌ No locations found")
        return
    
    print(f"📍 Testing with location: {location.location_name}")
    
    # Test increase action
    print("\n🚀 Testing 'increase' action...")
    request = factory.post(f'/queue/admin/adjust-workers/{location.id}/', {
        'action': 'increase'
    })
    request.user = user
    
    try:
        response = adjust_workers(request, location.id)
        response_data = json.loads(response.content.decode())
        
        print(f"Response status: {response.status_code}")
        print(f"Response data: {response_data}")
        
        if response_data.get('success'):
            print("✅ Increase action successful")
        else:
            print(f"❌ Increase action failed: {response_data.get('message')}")
            
    except Exception as e:
        print(f"❌ Error testing increase action: {e}")
        import traceback
        traceback.print_exc()
    
    # Test decrease action
    print("\n🛑 Testing 'decrease' action...")
    request = factory.post(f'/queue/admin/adjust-workers/{location.id}/', {
        'action': 'decrease'
    })
    request.user = user
    
    try:
        response = adjust_workers(request, location.id)
        response_data = json.loads(response.content.decode())
        
        print(f"Response status: {response.status_code}")
        print(f"Response data: {response_data}")
        
        if response_data.get('success'):
            print("✅ Decrease action successful")
        else:
            print(f"❌ Decrease action failed: {response_data.get('message')}")
            
    except Exception as e:
        print(f"❌ Error testing decrease action: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ajax_endpoint()
