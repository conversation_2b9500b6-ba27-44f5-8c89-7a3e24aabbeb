#!/usr/bin/env python
"""
Test script for worker management functionality
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from locations.models import Location
from queue_system.views import get_location_workers, sync_location_worker_count
import psutil

def test_worker_detection():
    """Test if we can detect running Celery workers"""
    print("🔍 Testing Worker Detection")
    print("=" * 50)
    
    # Get all locations
    locations = Location.objects.all()
    
    if not locations:
        print("❌ No locations found in database")
        return
    
    for location in locations:
        print(f"\n📍 Location: {location.location_name} (ID: {location.id})")
        
        # Get workers for this location
        workers = get_location_workers(location)
        print(f"   Found {len(workers)} workers:")
        
        for worker in workers:
            print(f"   - PID {worker['pid']}: {worker['cmdline'][:100]}...")
        
        # Sync worker count
        actual_count = sync_location_worker_count(location)
        print(f"   Synced worker count: {actual_count}")

def test_all_celery_processes():
    """Show all Celery processes running on the system"""
    print("\n🔍 All Celery Processes")
    print("=" * 50)

    celery_processes = []

    for proc in psutil.process_iter():
        try:
            proc_info = proc.as_dict(attrs=['pid', 'name', 'cmdline'])
            if proc_info['name'] and 'celery' in proc_info['name'].lower():
                cmdline = ' '.join(proc_info['cmdline']) if proc_info['cmdline'] else ''
                celery_processes.append({
                    'pid': proc_info['pid'],
                    'name': proc_info['name'],
                    'cmdline': cmdline
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess, PermissionError):
            continue
        except Exception as e:
            continue

    if celery_processes:
        print(f"Found {len(celery_processes)} Celery processes:")
        for proc in celery_processes:
            print(f"  PID {proc['pid']} ({proc['name']}): {proc['cmdline'][:100]}...")
    else:
        print("No Celery processes found")

if __name__ == "__main__":
    print("🚀 Worker Management Test")
    print("=" * 50)
    
    test_all_celery_processes()
    test_worker_detection()
    
    print("\n✅ Test completed")
